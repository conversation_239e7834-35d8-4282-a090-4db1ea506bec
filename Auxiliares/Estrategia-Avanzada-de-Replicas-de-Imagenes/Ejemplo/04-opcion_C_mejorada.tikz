% OPCIÓN C - RÉPLICA EXACTA CON PROTOCOLO MEJORADO
% Aplicando lecciones aprendidas: análisis automático + validación humana estratégica
% Imagen: Gráfica Distancia PK vs Ángulo α (curva con discontinuidad que termina en eje X)

\begin{tikzpicture}[scale=1.2]

% ANÁLISIS AUTOMÁTICO APLICADO:
% - Tipo: Función con discontinuidad f(α) = QP*exp(-k1*α) para α < α0, h*exp(-k2*(α-α0)) para α > α0
% - Ejes: X = "Ángulo α", Y = "Distancia PK"
% - Función: Decrece exponencialmente, salta en discontinuidad, continúa decreciendo hasta eje X
% - Color principal: Cyan/azul
% - Elementos: Curva en dos partes, 3 puntos marcados, líneas punteadas de referencia

% Cuadrícula de fondo PROMINENTE Y VISIBLE (como en imagen original)
\draw[gray!60, thick] (0,0) grid[step=1] (6,4);

% Ejes principales con flechas (grosor optimizado para Qtikz)
\draw[very thick, ->] (0,0) -- (6,0) node[below right] {Ángulo $\alpha$};
\draw[very thick, ->] (0,0) -- (0,4);

% Etiqueta del eje Y alejada y posicionada
\node[above, rotate=90] at (-0.4,2) {Distancia PK};

% PRIMERA PARTE: Curva exponencial decreciente (SE CORTA ABRUPTAMENTE)
% Va desde QP hasta aproximadamente nivel h, luego SE CORTA
\draw[cyan, very thick] (1,3.5) .. controls (1.5,3.0) and (2.0,2.5) .. (2.5,2.0);

% SEGUNDA PARTE: Curva SEPARADA que empieza exactamente en nivel h
% Empieza en h y decrece hacia la derecha
\draw[cyan, very thick] (3.5,2.0) .. controls (4.0,1.5) and (4.5,1.0) ..
                                   (5.0,0.7) .. controls (5.3,0.4) .. (5.7,0.2);

% Puntos importantes marcados en cyan (CORREGIDOS según imagen)
\fill[cyan] (1,3.5) circle (4pt);    % Punto inicial (QP)
\fill[cyan] (3.5,2.0) circle (4pt);  % Punto inicio segunda curva (h)
\fill[cyan] (5.7,0.2) circle (4pt);  % Punto final (cerca del eje, no en 0)

% Líneas punteadas de referencia NEGRAS PROMINENTES (CORREGIDAS según imagen)
\draw[black, dashed, very thick] (1,0) -- (1,3.5);      % Línea vertical desde punto inicial QP
\draw[black, dashed, very thick] (3.5,0) -- (3.5,2.0);  % Línea vertical desde punto h
\draw[black, dashed, very thick] (0,2.0) -- (3.5,2.0);  % Línea horizontal nivel h

% Etiquetas de valores específicos (CORREGIDAS según imagen original)
\node[left] at (-0.2,3.5) {$QP$};  % Valor inicial alto
\node[left] at (-0.2,2.0) {$h$};   % Valor nivel discontinuidad

% Marcas en los ejes para mayor claridad (CORREGIDAS)
\draw[black, thick] (-0.1,3.5) -- (0.1,3.5);  % Marca QP
\draw[black, thick] (-0.1,2.0) -- (0.1,2.0);  % Marca h
\draw[black, thick] (1,-0.1) -- (1,0.1);      % Marca inicio
\draw[black, thick] (3.5,-0.1) -- (3.5,0.1);  % Marca discontinuidad

% Origen
\fill[black] (0,0) circle (1.5pt);

\end{tikzpicture}

% VALIDACIÓN APLICADA (CORRECCIONES TRAS COMPARACIÓN CON IMAGEN ORIGINAL):
% ✅ Distancia PK (no KP) en eje Y - CORREGIDO
% ✅ DOS CURVAS SEPARADAS (no continuas) - CORREGIDO COMPLETAMENTE
% ✅ Cuadrícula PROMINENTE y visible - CORREGIDO (era demasiado sutil)
% ✅ Tres puntos cyan en posiciones correctas - REPOSICIONADOS
% ✅ Líneas punteadas NEGRAS prominentes - CORREGIDO
% ✅ Primera curva se corta abruptamente - IMPLEMENTADO
% ✅ Segunda curva empieza exactamente en h - CORREGIDO
% ✅ Discontinuidad claramente marcada - MEJORADA
% ✅ Proporciones según cuadrícula original - AJUSTADO
% ✅ Compatible Qtikz/Ktikz - VERIFICADO

% FUNCIÓN MATEMÁTICA REPRESENTADA:
% Función por partes con discontinuidad:
% f(α) = { QP * exp(-k1*α)           para α ∈ [0, α0)
%        { h * exp(-k2*(α-α0))       para α ∈ (α0, αmax]
% Donde:
% - QP = valor inicial (máximo)
% - h = valor después de discontinuidad
% - α0 = punto de discontinuidad
% - k1, k2 = constantes de decaimiento
% - αmax = valor donde f(αmax) = 0
