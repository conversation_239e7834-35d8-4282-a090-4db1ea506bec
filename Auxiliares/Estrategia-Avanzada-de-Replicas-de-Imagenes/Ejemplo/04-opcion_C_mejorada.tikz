% OPCIÓN C - RÉPLICA EXACTA CON PROTOCOLO MEJORADO
% Aplicando lecciones aprendidas: análisis automático + validación humana estratégica
% Imagen: Gráfica Distancia PK vs Ángulo α (curva con discontinuidad que termina en eje X)

\begin{tikzpicture}[scale=1.2]

% ANÁLISIS AUTOMÁTICO APLICADO:
% - Tipo: Función con discontinuidad f(α) = QP*exp(-k1*α) para α < α0, h*exp(-k2*(α-α0)) para α > α0
% - Ejes: X = "Ángulo α", Y = "Distancia PK"
% - Función: Decrece exponencialmente, salta en discontinuidad, continúa decreciendo hasta eje X
% - Color principal: Cyan/azul
% - Elementos: Curva en dos partes, 3 puntos marcados, líneas punteadas de referencia

% Cuadrícula de fondo sutil (como en imagen original)
\draw[gray!20, thin] (0,0) grid[step=0.5] (5.5,3.8);

% Ejes principales con flechas (grosor optimizado para Qtikz)
\draw[thick, ->] (0,0) -- (5.5,0) node[below right] {Ángulo $\alpha$};
\draw[thick, ->] (0,0) -- (0,3.8);

% Etiqueta del eje Y alejada y posicionada
\node[above, rotate=90] at (-0.6,1.2) {Distancia PK};

% PRIMERA PARTE: Curva exponencial decreciente (antes de discontinuidad)
% Función matemática: f(α) = QP * exp(-k1*α) para α ∈ [0, α0)
\draw[cyan, very thick] (0.7,3.2) .. controls (1.1,2.8) and (1.5,2.4) .. 
                                   (1.9,2.0) .. controls (2.1,1.9) .. (2.3,1.8);

% SEGUNDA PARTE: Curva exponencial que continúa hasta eje X (después de discontinuidad)
% Función matemática: f(α) = h * exp(-k2*(α-α0)) para α ∈ (α0, αmax]
\draw[cyan, very thick] (2.7,1.4) .. controls (3.2,1.0) and (3.8,0.6) .. 
                                   (4.4,0.3) .. controls (4.7,0.1) .. (5.2,0);

% Puntos importantes marcados en cyan
\fill[cyan] (0.7,3.2) circle (3pt);  % Punto inicial (QP)
\fill[cyan] (2.7,1.4) circle (3pt);  % Punto después de discontinuidad (h)
\fill[cyan] (5.2,0) circle (3pt);    % Punto final en eje X

% Líneas punteadas de referencia NEGRAS PROMINENTES (como en original)
\draw[black, dashed, very thick] (0.7,0) -- (0.7,3.2);    % Línea vertical desde punto inicial
\draw[black, dashed, very thick] (2.7,0) -- (2.7,1.4);    % Línea vertical desde punto h
\draw[black, dashed, very thick] (0,1.4) -- (2.7,1.4);    % Línea horizontal nivel h

% Etiquetas de valores específicos (ajustadas a posiciones de imagen original)
\node[left] at (-0.1,3.2) {$QP$};  % Valor inicial alto
\node[left] at (-0.1,1.4) {$h$};   % Valor después de discontinuidad

% Marcas en los ejes para mayor claridad (ajustadas)
\draw[black] (-0.05,3.2) -- (0.05,3.2);  % Marca QP
\draw[black] (-0.05,1.4) -- (0.05,1.4);  % Marca h
\draw[black] (0.7,-0.05) -- (0.7,0.05);  % Marca inicio
\draw[black] (2.7,-0.05) -- (2.7,0.05);  % Marca discontinuidad
\draw[black] (5.2,-0.05) -- (5.2,0.05);  % Marca final

% Origen
\fill[black] (0,0) circle (1.5pt);

\end{tikzpicture}

% VALIDACIÓN APLICADA (ANÁLISIS DE IMAGEN ORIGINAL):
% ✅ Distancia PK (no KP) en eje Y - CORREGIDO
% ✅ Curva con discontinuidad cyan - CONFIRMADO
% ✅ Tres puntos cyan: inicio (QP), discontinuidad (h), final (eje X) - AÑADIDOS
% ✅ Líneas punteadas NEGRAS prominentes - CORREGIDO (eran grises)
% ✅ Cuadrícula de fondo sutil - AÑADIDA (como en original)
% ✅ Etiquetas QP y h correctas - VERIFICADO Y REPOSICIONADO
% ✅ Curva termina en eje X - CORREGIDO (antes flotaba)
% ✅ Discontinuidad bien marcada - MEJORADA
% ✅ Proporciones fieles a imagen original - AJUSTADO
% ✅ Compatible Qtikz/Ktikz - VERIFICADO

% FUNCIÓN MATEMÁTICA REPRESENTADA:
% Función por partes con discontinuidad:
% f(α) = { QP * exp(-k1*α)           para α ∈ [0, α0)
%        { h * exp(-k2*(α-α0))       para α ∈ (α0, αmax]
% Donde:
% - QP = valor inicial (máximo)
% - h = valor después de discontinuidad
% - α0 = punto de discontinuidad
% - k1, k2 = constantes de decaimiento
% - αmax = valor donde f(αmax) = 0
