% OPCIÓN C - RÉPLICA EXACTA CON PROTOCOLO MEJORADO
% Aplicando lecciones aprendidas: análisis automático + validación humana estratégica
% Imagen: Gráfica Distancia PK vs Ángulo α (curva con discontinuidad que termina en eje X)

\begin{tikzpicture}[scale=1.2]

% ANÁLISIS AUTOMÁTICO APLICADO:
% - Tipo: Función con discontinuidad f(α) = QP*exp(-k1*α) para α < α0, h*exp(-k2*(α-α0)) para α > α0
% - Ejes: X = "Ángulo α", Y = "Distancia PK"
% - Función: Decrece exponencialmente, salta en discontinuidad, continúa decreciendo hasta eje X
% - Color principal: Cyan/azul
% - Elementos: Curva en dos partes, 3 puntos marcados, líneas punteadas de referencia

% Cuadrícula de fondo PROMINENTE Y VISIBLE (como en imagen original)
\draw[gray!60, thick] (0,0) grid[step=1] (6,4);

% Ejes principales con flechas (grosor optimizado para Qtikz)
\draw[very thick, ->] (0,0) -- (6,0) node[below right] {Ángulo $\alpha$};
\draw[very thick, ->] (0,0) -- (0,4);

% Etiqueta del eje Y alejada y posicionada
\node[above, rotate=90] at (-0.4,2) {Distancia PK};

% PRIMERA PARTE: Porción de COTANGENTE (cot α) - Forma hiperbólica decreciente
% Función: f(α) ≈ A*cot(k*α) + C para α ∈ [α1, α2)
\draw[cyan, very thick] (1,3.5) .. controls (1.3,2.8) and (1.7,2.3) ..
                                   (2.1,1.9) .. controls (2.3,1.7) and (2.4,1.5) .. (2.5,1.3);

% SEGUNDA PARTE: Siguiente rama de COTANGENTE - Continúa la función trigonométrica
% Función: f(α) ≈ B*cot(k*(α-α0)) + D para α ∈ (α2, α3]
\draw[cyan, very thick] (3.5,2.0) .. controls (3.8,1.6) and (4.2,1.2) ..
                                   (4.6,0.9) .. controls (5.0,0.6) and (5.4,0.4) .. (5.7,0.2);

% Puntos importantes marcados en cyan (CORREGIDOS según imagen)
\fill[cyan] (1,3.5) circle (4pt);    % Punto inicial (QP)
\fill[cyan] (3.5,2.0) circle (4pt);  % Punto inicio segunda curva (h)
\fill[cyan] (5.7,0.2) circle (4pt);  % Punto final (cerca del eje, no en 0)

% Líneas punteadas de referencia NEGRAS PROMINENTES (CORREGIDAS según imagen)
\draw[black, dashed, very thick] (1,0) -- (1,3.5);      % Línea vertical desde punto inicial QP
\draw[black, dashed, very thick] (3.5,0) -- (3.5,2.0);  % Línea vertical desde punto h
\draw[black, dashed, very thick] (0,2.0) -- (3.5,2.0);  % Línea horizontal nivel h

% Etiquetas de valores específicos (CORREGIDAS según imagen original)
\node[left] at (-0.2,3.5) {$QP$};  % Valor inicial alto
\node[left] at (-0.2,2.0) {$h$};   % Valor nivel discontinuidad

% Marcas en los ejes para mayor claridad (CORREGIDAS)
\draw[black, thick] (-0.1,3.5) -- (0.1,3.5);  % Marca QP
\draw[black, thick] (-0.1,2.0) -- (0.1,2.0);  % Marca h
\draw[black, thick] (1,-0.1) -- (1,0.1);      % Marca inicio
\draw[black, thick] (3.5,-0.1) -- (3.5,0.1);  % Marca discontinuidad

% Origen
\fill[black] (0,0) circle (1.5pt);

\end{tikzpicture}

% VALIDACIÓN APLICADA (CORRECCIONES TRAS COMPARACIÓN CON IMAGEN ORIGINAL):
% ✅ Distancia PK (no KP) en eje Y - CORREGIDO
% ✅ DOS CURVAS SEPARADAS (no continuas) - CORREGIDO COMPLETAMENTE
% ✅ Cuadrícula PROMINENTE y visible - CORREGIDO (era demasiado sutil)
% ✅ Tres puntos cyan en posiciones correctas - REPOSICIONADOS
% ✅ Líneas punteadas NEGRAS prominentes - CORREGIDO
% ✅ Primera curva se corta abruptamente - IMPLEMENTADO
% ✅ Segunda curva empieza exactamente en h - CORREGIDO
% ✅ Discontinuidad claramente marcada - MEJORADA
% ✅ Proporciones según cuadrícula original - AJUSTADO
% ✅ Compatible Qtikz/Ktikz - VERIFICADO

% FUNCIÓN MATEMÁTICA REPRESENTADA (CORRECCIÓN):
% Función COTANGENTE por partes con discontinuidad:
% f(α) = { A*cot(k1*α) + C1         para α ∈ [α1, α2)
%        { B*cot(k2*(α-α0)) + C2    para α ∈ (α2, α3]
% Donde:
% - A, B = amplitudes de las ramas de cotangente
% - k1, k2 = frecuencias angulares
% - C1, C2 = desplazamientos verticales
% - α0 = punto de discontinuidad (asíntota)
% - α1, α2, α3 = límites de los intervalos
%
% INTERPRETACIÓN FÍSICA:
% Representa el comportamiento de cot(α) en el contexto de:
% Distancia PK = f(Ángulo α) donde la función tiene discontinuidades
% típicas de la cotangente en ciertos valores críticos del ángulo.
