% OPCIÓN A - RÉPLICA EXACTA CON PROTOCOLO MEJORADO
% Aplicando lecciones aprendidas: análisis automático + validación humana estratégica
% Imagen: Gráfica Distancia PK vs Ángulo α (línea horizontal constante)

\begin{tikzpicture}[scale=1.2]

% ANÁLISIS AUTOMÁTICO APLICADO:
% - Tipo: Gráfica de función constante
% - Ejes: X = "Ángulo α", Y = "Distancia PK" 
% - Función: f(α) = h (constante horizontal)
% - Color principal: Cyan/azul
% - Elementos: Ejes con flechas, línea horizontal, marca h

% Ejes principales con flechas (grosor optimizado para Qtikz)
\draw[thick, ->] (0,0) -- (5.5,0) node[below right] {Ángulo $\alpha$};
\draw[thick, ->] (0,0) -- (0,3.2) node[above left, rotate=90] {Distancia PK};

% Línea horizontal constante - CARACTERÍSTICA PRINCIPAL OPCIÓN A
\draw[cyan, very thick] (0.2,2.0) -- (5.2,2.0);

% Marca de altura "h" con línea vertical de referencia
\draw[black, thick] (1.8,0) -- (1.8,2.0);
\node[right] at (1.9,1.0) {$h$};

% Línea horizontal de referencia punteada (sutil)
\draw[gray!40, dashed, thin] (0,2.0) -- (5.5,2.0);

% Marcas en los ejes (opcional para mayor claridad)
\draw[black] (-0.05,2.0) -- (0.05,2.0);
\draw[black] (1.8,-0.05) -- (1.8,0.05);

% Origen
\fill[black] (0,0) circle (1.5pt);

\end{tikzpicture}

% VALIDACIÓN APLICADA:
% ✅ Distancia PK (no KP) en eje Y - CORREGIDO
% ✅ Línea horizontal constante cyan - CONFIRMADO  
% ✅ Marca h con línea vertical - AÑADIDO
% ✅ Ejes con flechas apropiadas - OPTIMIZADO
% ✅ Proporciones fieles a imagen original - AJUSTADO
% ✅ Compatible Qtikz/Ktikz - VERIFICADO
