% OPCIÓN C - Réplica Exacta (Protocolo Mejorado Aplicado)
% Gráfica: Distancia PK vs Ángulo α - Curva con discontinuidad que termina en eje X
\begin{tikzpicture}[scale=1.0]

% Ejes principales con flechas
\draw[thick, ->] (0,0) -- (5,0) node[below right] {Ángulo $\alpha$};
\draw[thick, ->] (0,0) -- (0,3.5);

% Etiqueta del eje Y alejada y posicionada
\node[above, rotate=90] at (-0.6,1.2) {Distancia PK};

% Primera parte de la curva (antes de la discontinuidad) - exponencial decreciente
\draw[cyan, very thick] (0.8,3.0) .. controls (1.2,2.6) and (1.6,2.2) .. (2.0,1.8);

% Segunda parte de la curva (después de discontinuidad) - continúa hasta eje X
\draw[cyan, very thick] (2.4,1.4) .. controls (3.2,0.8) and (4.0,0.3) .. (4.8,0);

% Puntos importantes marcados en cyan
\fill[cyan] (0.8,3.0) circle (3pt);  % Punto inicial (QP)
\fill[cyan] (2.4,1.4) circle (3pt);  % Punto después de discontinuidad (h)
\fill[cyan] (4.8,0) circle (3pt);    % Punto final en eje X

% Líneas punteadas de referencia NEGRAS (como en imagen original)
\draw[black, dashed, thick] (0.8,0) -- (0.8,3.0);    % Línea vertical desde punto inicial
\draw[black, dashed, thick] (2.4,0) -- (2.4,1.4);    % Línea vertical desde punto h
\draw[black, dashed, thick] (0,1.4) -- (2.4,1.4);    % Línea horizontal nivel h

% Etiquetas específicas
\node[left] at (-0.1,3.0) {$QP$};  % Valor inicial alto
\node[left] at (-0.1,1.4) {$h$};   % Valor después de discontinuidad

% Origen marcado
\fill[black] (0,0) circle (1pt);

\end{tikzpicture}
