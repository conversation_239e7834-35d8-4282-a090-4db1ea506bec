This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2026/dev/Arch Linux) (preloaded format=pdflatex 2025.6.3)  8 JUL 2025 22:04
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**06-opcion_C_completa.tex
(./06-opcion_C_completa.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/share/texmf-dist/tex/latex/standalone/standalone.cls
Document Class: standalone 2025/02/22 v1.5a Class to compile TeX sub-files stan
dalone
(/usr/share/texmf-dist/tex/latex/tools/shellesc.sty
Package: shellesc 2023/07/08 v1.0d unified shell escape interface for LaTeX
Package shellesc Info: Restricted shell escape enabled on input line 77.
)
(/usr/share/texmf-dist/tex/generic/iftex/ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.

(/usr/share/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
(/usr/share/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(/usr/share/texmf-dist/tex/generic/xkeyval/xkeyval.tex
(/usr/share/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks17
\XKV@tempa@toks=\toks18

(/usr/share/texmf-dist/tex/generic/xkeyval/keyval.tex))
\XKV@depth=\count196
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
\sa@internal=\count197
\c@sapage=\count198

(/usr/share/texmf-dist/tex/latex/standalone/standalone.cfg
File: standalone.cfg 2025/02/22 v1.5a Default configuration file for 'standalon
e' class
)
(/usr/share/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/share/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count199
\c@section=\count266
\c@subsection=\count267
\c@subsubsection=\count268
\c@paragraph=\count269
\c@subparagraph=\count270
\c@figure=\count271
\c@table=\count272
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
\sa@box=\box52
)
(/usr/share/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks19
\pgfutil@tempdima=\dimen142
\pgfutil@tempdimb=\dimen143
)
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box53
)
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/usr/share/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(/usr/share/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(/usr/share/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen144
\Gin@req@width=\dimen145
)
(/usr/share/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks20
\pgfkeys@temptoks=\toks21

(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.te
x
\pgfkeys@tmptoks=\toks22
))
\pgf@x=\dimen146
\pgf@y=\dimen147
\pgf@xa=\dimen148
\pgf@ya=\dimen149
\pgf@xb=\dimen150
\pgf@yb=\dimen151
\pgf@xc=\dimen152
\pgf@yc=\dimen153
\pgf@xd=\dimen154
\pgf@yd=\dimen155
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count273
\c@pgf@countb=\count274
\c@pgf@countc=\count275
\c@pgf@countd=\count276
\t@pgf@toka=\toks23
\t@pgf@tokb=\toks24
\t@pgf@tokc=\toks25
\pgf@sys@id@count=\count277
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count278
\pgfsyssoftpath@bigbuffer@items=\count279
)
(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/share/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(/usr/share/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(/usr/share/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen156
\pgfmath@count=\count280
\pgfmath@box=\box54
\pgfmath@toks=\toks26
\pgfmath@stack@operand=\toks27
\pgfmath@stack@operation=\toks28
)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code
.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.te
x) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics
.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count281
))
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen157
\pgf@picmaxx=\dimen158
\pgf@picminy=\dimen159
\pgf@picmaxy=\dimen160
\pgf@pathminx=\dimen161
\pgf@pathmaxx=\dimen162
\pgf@pathminy=\dimen163
\pgf@pathmaxy=\dimen164
\pgf@xx=\dimen165
\pgf@xy=\dimen166
\pgf@yx=\dimen167
\pgf@yy=\dimen168
\pgf@zx=\dimen169
\pgf@zy=\dimen170
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen171
\pgf@path@lasty=\dimen172
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen173
\pgf@shorten@start@additional=\dimen174
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box55
\pgf@hbox=\box56
\pgf@layerbox@main=\box57
\pgf@picture@serial@count=\count282
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen175
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.t
ex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen176
\pgf@pt@y=\dimen177
\pgf@pt@temp=\dimen178
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.te
x
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen179
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen180
\pgf@sys@shading@range@num=\count283
\pgf@shadingcount=\count284
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box58
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box59
)
(/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen181
\pgf@nodesepend=\dimen182
)
(/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/share/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex))
(/usr/share/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen183
\pgffor@skip=\dimen184
\pgffor@stack=\toks29
\pgffor@toks=\toks30
))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.te
x
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count285
\pgfplotmarksize=\dimen185
)
\tikz@lastx=\dimen186
\tikz@lasty=\dimen187
\tikz@lastxsaved=\dimen188
\tikz@lastysaved=\dimen189
\tikz@lastmovetox=\dimen190
\tikz@lastmovetoy=\dimen191
\tikzleveldistance=\dimen192
\tikzsiblingdistance=\dimen193
\tikz@figbox=\box60
\tikz@figbox@bg=\box61
\tikz@tempbox=\box62
\tikz@tempbox@bg=\box63
\tikztreelevel=\count286
\tikznumberofchildren=\count287
\tikznumberofcurrentchild=\count288
\tikz@fig@count=\count289
 (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count290
\pgfmatrixcurrentcolumn=\count291
\pgf@matrix@numberofcolumns=\count292
)
\tikz@expandcount=\count293

(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
topaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/latex/pgfplots/pgfplots.sty
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.revision.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)

(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotscore.code.tex
\t@pgfplots@toka=\toks31
\t@pgfplots@tokb=\toks32
\t@pgfplots@tokc=\toks33
\pgfplots@tmpa=\dimen194
\c@pgfplots@coordindex=\count294
\c@pgfplots@scanlineindex=\count295

(/usr/share/texmf-dist/tex/generic/pgfplots/sys/pgfplotssysgeneric.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/libs/pgfplotslibrary.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfsupp_l
oader.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryfpu.code.tex)
Package pgfplots: loading complementary utilities for your pgf version...
\t@pgf@toka=\toks34
\t@pgf@tokb=\toks35
\t@pgf@tokc=\toks36

(/usr/share/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfsupp_p
gfutil-common-lists.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststructure
.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststructure
ext.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsarray.code.te
x
\c@pgfplotsarray@tmp=\count296
)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsmatrix.code.t
ex)
(/usr/share/texmf-dist/tex/generic/pgfplots/numtable/pgfplotstableshared.code.t
ex
\c@pgfplotstable@counta=\count297
\t@pgfplotstable@a=\toks37
)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsdeque.code.te
x) (/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.data.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.verb.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/libs/pgflibrarypgfplots.surfshading
.code.tex
\c@pgfplotslibrarysurf@no=\count298

(/usr/share/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots.surfshading.
pgfsys-pdftex.def)))
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotscolormap.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotscolor.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsstackedplots.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsplothandlers.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplothandler.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplotimage.code.tex)))
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.scaling.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotscoordprocessing.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.errorbars.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.markers.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsticks.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.paths.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduledecorations.code.tex
\pgfdecoratedcompleteddistance=\dimen195
\pgfdecoratedremainingdistance=\dimen196
\pgfdecoratedinputsegmentcompleteddistance=\dimen197
\pgfdecoratedinputsegmentremainingdistance=\dimen198
\pgf@decorate@distancetomove=\dimen199
\pgf@decorate@repeatstate=\count299
\pgfdecorationsegmentamplitude=\dimen256
\pgfdecorationsegmentlength=\dimen257
)
\tikz@lib@dec@box=\box64
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.pathmorphing.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorati
ons.pathmorphing.code.tex))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.pathreplacing.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorati
ons.pathreplacing.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/libs/tikzlibrarypgfplots.contourlua
.code.tex)
\pgfplots@numplots=\count300
\pgfplots@xmin@reg=\dimen258
\pgfplots@xmax@reg=\dimen259
\pgfplots@ymin@reg=\dimen260
\pgfplots@ymax@reg=\dimen261
\pgfplots@zmin@reg=\dimen262
\pgfplots@zmax@reg=\dimen263
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
plotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryplotmarks.code.tex
File: pgflibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/share/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/share/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks38
\ex@=\dimen264
))
(/usr/share/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen265
)
(/usr/share/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count301
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count302
\leftroot@=\count303
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count304
\DOTSCASE@=\count305
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box65
\strutbox@=\box66
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen266
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count306
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count307
\dotsspace@=\muskip17
\c@parentequation=\count308
\dspbrk@lvl=\count309
\tag@help=\toks39
\row@=\count310
\column@=\count311
\maxfields@=\count312
\andhelp@=\toks40
\eqnshift@=\dimen267
\alignsep@=\dimen268
\tagshift@=\dimen269
\tagwidth@=\dimen270
\totwidth@=\dimen271
\lineht@=\dimen272
\@envbody=\toks41
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks42
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(/usr/share/texmf-dist/tex/latex/pgf/frontendlayer/libraries/tikzlibraryexterna
l.code.tex (/usr/share/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(/usr/share/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(/usr/share/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)
(/usr/share/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzexterna
lshared.code.tex))

! Package pgfkeys Error: I do not know the key '/pgfplots/declare function', to
 which you passed ' cotangent(\x ) = 2.5*cot(\x *15 + 10) + 1.5; ', and I am go
ing to ignore it. Perhaps you misspelled it.

See the pgfkeys package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.26 }
      
This error message was generated by an \errmessage
command, so I can't give any explicit help.
Pretend that you're Hercule Poirot: Examine all clues,
and deduce the truth by order and method.

(/usr/share/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count313
\l__pdf_internal_box=\box67
)
(./06-opcion_C_completa.aux)
\openout1 = `06-opcion_C_completa.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 28.
LaTeX Font Info:    ... okay on input line 28.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 28.
LaTeX Font Info:    ... okay on input line 28.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 28.
LaTeX Font Info:    ... okay on input line 28.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 28.
LaTeX Font Info:    ... okay on input line 28.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 28.
LaTeX Font Info:    ... okay on input line 28.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 28.
LaTeX Font Info:    ... okay on input line 28.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 28.
LaTeX Font Info:    ... okay on input line 28.

(/usr/share/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count314
\scratchdimen=\dimen273
\scratchbox=\box68
\nofMPsegments=\count315
\nofMParguments=\count316
\everyMPshowfont=\toks43
\MPscratchCnt=\count317
\MPscratchDim=\dimen274
\MPnumerator=\count318
\makeMPintoPDFobject=\count319
\everyMPtoPDFconversion=\toks44
) (/usr/share/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(/usr/share/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
Package pgfplots notification 'compat/show suggested version=true': document ha
s been generated with the most recent feature set (\pgfplotsset{compat=1.18}).

Runaway argument?
 axis lines=middle, axis line style={very thick,<->}, xmin=0, xmax=6,\ETC.
! Paragraph ended before \pgfplots@@environment@axis was complete.
<to be read again> 
                   \par 
l.41 
     
I suspect you've forgotten a `}', causing me to apply this
control sequence to too much text. How can we recover?
My plan is to forget the whole thing and hope for the best.

Missing character: There is no x in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no A in font nullfont!
Missing character: There is no ^^S in font nullfont!
Missing character: There is no A in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no D in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no P in font nullfont!
Missing character: There is no K in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no x in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no w in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no h in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no 9 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no j in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no j in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no h in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no k in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no x in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no / in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no h in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no k in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no h in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no 5 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no : in font nullfont!
Missing character: There is no 4 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no w in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no h in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no ] in font nullfont!
! Undefined control sequence.
l.69 \addplot
             [domain=0.3:5.7, cyan, very thick, samples=500] {cotangent(x)};
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

Missing character: There is no [ in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 3 in font nullfont!
Missing character: There is no : in font nullfont!
Missing character: There is no 5 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no h in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no k in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no 5 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no x in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
! Undefined control sequence.
l.72 \addplot
             [only marks, cyan, mark=*, mark size=4pt]
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

Missing character: There is no [ in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no k in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no k in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no * in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no k in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no z in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no = in font nullfont!
Missing character: There is no 4 in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 3 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 5 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 2 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 8 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 4 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 5 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 5 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 3 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
! Undefined control sequence.
\endpgfplots@environment@opt ...ctiveplothandlers 
                                                  \pgfplots@set@options@afte...
l.75 \end{axis}
               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@x 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@x 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@y 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@y 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@x 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no s in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

Missing character: There is no 0 in font nullfont!
Missing character: There is no 0 in font nullfont!
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@y 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no s in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

Missing character: There is no 0 in font nullfont!
Missing character: There is no 0 in font nullfont!
! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@@min 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@@min 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no p in font nullfont!
Missing character: There is no t in font nullfont!
! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@@max 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@@max 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no p in font nullfont!
Missing character: There is no t in font nullfont!
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@x 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no x in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@x 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no x in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.


Package pgfplots Warning: Axis range for axis x is approximately empty; enlargi
ng it (it is [227.62204:227.62204]) on input line 75.

 [warning /pgfplots/warning/approx empty range enlarged]
! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@@min 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@@min 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no p in font nullfont!
Missing character: There is no t in font nullfont!
! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@@max 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@@max 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no p in font nullfont!
Missing character: There is no t in font nullfont!
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@y 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no x in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@y 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no x in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.


Package pgfplots Warning: Axis range for axis y is approximately empty; enlargi
ng it (it is [227.62204:227.62204]) on input line 75.

 [warning /pgfplots/warning/approx empty range enlarged]
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@x 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

Missing character: There is no - in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 0 in font nullfont!
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@x 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

Missing character: There is no 1 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 0 in font nullfont!
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@y 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

Missing character: There is no - in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 0 in font nullfont!
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@y 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

Missing character: There is no 1 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 0 in font nullfont!
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@x 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no e in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

Missing character: There is no - in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 0 in font nullfont!
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@x 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no x in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

Missing character: There is no - in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 0 in font nullfont!
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@x 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no e in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

Missing character: There is no 1 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 0 in font nullfont!
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@x 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no x in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

Missing character: There is no 1 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 0 in font nullfont!
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@y 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no e in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

Missing character: There is no - in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 0 in font nullfont!
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@y 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no x in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

Missing character: There is no - in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 0 in font nullfont!
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@y 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no s in font nullfont!
Missing character: There is no e in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

Missing character: There is no 1 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 0 in font nullfont!
! Missing \endcsname inserted.
<to be read again> 
                   \pgfcrdmth@y 
l.75 \end{axis}
               
The control sequence marked <to be read again> should
not appear between \csname and \endcsname.

Missing character: There is no @ in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no x in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
! Extra \endcsname.
\pgfplotscoordmath ...@#1\endcsname @#2\endcsname 
                                                  
l.75 \end{axis}
               
I'm ignoring this, since I wasn't doing a \csname.

Missing character: There is no 1 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 0 in font nullfont!

! Package PGF Math Error: Sorry, an internal routine of the floating point unit
 got an ill-formatted floating point number `'. The unreadable part was near ''
..

See the PGF Math package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.75 \end{axis}
               
(That was another \errmessage.)

! Missing number, treated as zero.
<to be read again> 
                   n
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   n
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no n in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no t in font nullfont!

! Package PGF Math Error: Sorry, an internal routine of the floating point unit
 got an ill-formatted floating point number `'. The unreadable part was near ''
..

See the PGF Math package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.75 \end{axis}
               
(That was another \errmessage.)

! Missing number, treated as zero.
<to be read again> 
                   n
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   n
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no n in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no p in font nullfont!
Missing character: There is no t in font nullfont!
! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@xtickmin 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@xtickmin 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no p in font nullfont!
Missing character: There is no t in font nullfont!
! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@xtickmax 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@xtickmax 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@xtickmin 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@xtickmin 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@xtickmax 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@xtickmax 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@xtickmin 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@xtickmin 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@xtickmax 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@xtickmax 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no p in font nullfont!
Missing character: There is no t in font nullfont!
! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@xtickmin 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@xtickmin 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@xtickmax 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@xtickmax 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no p in font nullfont!
Missing character: There is no t in font nullfont!
! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@xtickmin 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@xtickmin 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@xtickmax 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@xtickmax 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no p in font nullfont!
Missing character: There is no t in font nullfont!
! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@xtickmin 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@xtickmin 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@xtickmax 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@xtickmax 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no p in font nullfont!
Missing character: There is no t in font nullfont!
! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@xtickmin 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@xtickmin 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@xtickmax 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@xtickmax 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no p in font nullfont!
Missing character: There is no t in font nullfont!
! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@ytickmin 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@ytickmin 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

Missing character: There is no p in font nullfont!
Missing character: There is no t in font nullfont!
! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@ytickmax 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@ytickmax 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@ytickmin 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@ytickmin 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@ytickmax 
l.75 \end{axis}
               
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

! Illegal unit of measure (pt inserted).
<to be read again> 
                   \pgfplots@ytickmax 
l.75 \end{axis}
               
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

! Missing number, treated as zero.
<to be read again> 
                   \pgfplots@ytickmin 
l.75 \end{axis}
               
(That makes 100 errors; please try again.) 
Here is how much of TeX's memory you used:
 23667 strings out of 475171
 621291 string characters out of 5767096
 1110502 words of memory out of 5000000
 46302 multiletter control sequences out of 15000+600000
 559291 words of font info for 38 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 102i,5n,107p,736b,858s stack positions out of 10000i,1000n,20000p,200000b,200000s

!  ==> Fatal error occurred, no output PDF file produced!
