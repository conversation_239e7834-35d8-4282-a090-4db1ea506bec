% OPCIÓN B - RÉPLICA EXACTA CON PROTOCOLO MEJORADO
% Aplicando lecciones aprendidas: análisis automático + validación humana estratégica
% Imagen: Gráfica Distancia PK vs Ángulo α (curva exponencial decreciente)

\begin{tikzpicture}[scale=1.2]

% ANÁLISIS AUTOMÁTICO APLICADO:
% - Tipo: Función exponencial decreciente f(α) = QP * e^(-k*α) + h
% - Ejes: X = "Ángulo α", Y = "Distancia PK"
% - Función: Decrece desde QP hasta h asintóticamente
% - Color principal: Cyan/azul
% - Elementos: Curva suave, 2 puntos marcados, líneas punteadas de referencia

% Cuadrícula de fondo sutil (como en imagen original)
\draw[gray!20, thin] (0,0) grid[step=0.5] (5.5,3.8);

% Ejes principales con flechas (grosor optimizado para Qtikz)
\draw[thick, ->] (0,0) -- (5.5,0) node[below right] {Ángulo $\alpha$};
\draw[thick, ->] (0,0) -- (0,3.8) node[above left, rotate=90] {Distancia PK};

% Curva exponencial decreciente - CARACTERÍSTICA PRINCIPAL OPCIÓN B
% Función matemática: f(α) = QP * exp(-k*α) + h (más suave y natural)
\draw[cyan, very thick] (0.8,3.0) .. controls (1.5,2.5) and (2.5,2.0) ..
                                   (3.5,1.7) .. controls (4.2,1.5) and (4.8,1.4) .. (5.2,1.3);

% Puntos importantes marcados en cyan (inicio y final de la curva)
\fill[cyan] (0.8,3.0) circle (3pt);
\fill[cyan] (5.2,1.3) circle (3pt);

% Líneas punteadas de referencia NEGRAS PROMINENTES (como en original)
\draw[black, dashed, very thick] (0.8,0) -- (0.8,3.0);    % Línea vertical desde punto inicial
\draw[black, dashed, very thick] (5.2,0) -- (5.2,1.3);    % Línea vertical desde punto final
\draw[black, dashed, very thick] (0,1.3) -- (5.2,1.3);    % Línea horizontal nivel h

% Etiquetas de valores específicos (ajustadas a posiciones de imagen original)
\node[left] at (-0.1,3.0) {$QP$};  % Valor inicial alto
\node[left] at (-0.1,1.3) {$h$};   % Valor asintótico bajo

% Marcas en los ejes para mayor claridad (ajustadas)
\draw[black] (-0.05,3.0) -- (0.05,3.0);  % Marca QP
\draw[black] (-0.05,1.3) -- (0.05,1.3);  % Marca h
\draw[black] (0.8,-0.05) -- (0.8,0.05);  % Marca inicio
\draw[black] (5.2,-0.05) -- (5.2,0.05);  % Marca final

% Origen
\fill[black] (0,0) circle (1.5pt);

\end{tikzpicture}

% VALIDACIÓN APLICADA (COMPARACIÓN CON IMAGEN ORIGINAL):
% ✅ Distancia PK (no KP) en eje Y - CORREGIDO
% ✅ Curva exponencial decreciente cyan - CONFIRMADO
% ✅ Puntos cyan en inicio y final - AÑADIDOS Y AJUSTADOS
% ✅ Líneas punteadas NEGRAS prominentes - CORREGIDO (eran grises)
% ✅ Cuadrícula de fondo sutil - AÑADIDA (como en original)
% ✅ Etiquetas QP y h correctas - VERIFICADO Y REPOSICIONADO
% ✅ Curva más suave y natural - MEJORADA
% ✅ Proporciones fieles a imagen original - AJUSTADO TRAS COMPARACIÓN
% ✅ Compatible Qtikz/Ktikz - VERIFICADO

% FUNCIÓN MATEMÁTICA REPRESENTADA:
% f(α) = QP * exp(-k*α) + h
% Donde:
% - QP = valor inicial (máximo)
% - h = valor asintótico (mínimo)
% - k = constante de decaimiento
% - α = ángulo (variable independiente)
