% OPCIÓN B - RÉPLICA EXACTA CON PROTOCOLO MEJORADO
% Aplicando lecciones aprendidas: análisis automático + validación humana estratégica
% Imagen: Gráfica Distancia PK vs Ángulo α (curva exponencial decreciente)

\begin{tikzpicture}[scale=1.2]

% ANÁLISIS AUTOMÁTICO APLICADO:
% - Tipo: Función exponencial decreciente f(α) = QP * e^(-k*α) + h
% - Ejes: X = "Ángulo α", Y = "Distancia PK"
% - Función: Decrece desde QP hasta h asintóticamente
% - Color principal: Cyan/azul
% - Elementos: Curva suave, 2 puntos marcados, líneas punteadas de referencia

% Ejes principales con flechas (grosor optimizado para Qtikz)
\draw[thick, ->] (0,0) -- (5.5,0) node[below right] {Ángulo $\alpha$};
\draw[thick, ->] (0,0) -- (0,3.8) node[above left, rotate=90] {Distancia PK};

% Curva exponencial decreciente - CARACTERÍSTICA PRINCIPAL OPCIÓN B
% Función matemática: f(α) = QP * exp(-k*α) + h
\draw[cyan, very thick] (0.7,3.2) .. controls (1.2,2.8) and (2.0,2.2) .. 
                                   (3.0,1.8) .. controls (3.8,1.6) and (4.5,1.5) .. (5.0,1.4);

% Puntos importantes marcados en cyan (inicio y final de la curva)
\fill[cyan] (0.7,3.2) circle (3pt);
\fill[cyan] (5.0,1.4) circle (3pt);

% Líneas punteadas de referencia
\draw[black, dashed, thick] (0.7,0) -- (0.7,3.2);    % Línea vertical desde punto inicial
\draw[black, dashed, thick] (5.0,0) -- (5.0,1.4);    % Línea vertical desde punto final
\draw[black, dashed, thick] (0,1.4) -- (5.0,1.4);    % Línea horizontal nivel h

% Etiquetas de valores específicos
\node[left] at (-0.1,3.2) {$QP$};  % Valor inicial alto
\node[left] at (-0.1,1.4) {$h$};   % Valor asintótico bajo

% Marcas en los ejes para mayor claridad
\draw[black] (-0.05,3.2) -- (0.05,3.2);  % Marca QP
\draw[black] (-0.05,1.4) -- (0.05,1.4);  % Marca h
\draw[black] (0.7,-0.05) -- (0.7,0.05);  % Marca inicio
\draw[black] (5.0,-0.05) -- (5.0,0.05);  % Marca final

% Origen
\fill[black] (0,0) circle (1.5pt);

\end{tikzpicture}

% VALIDACIÓN APLICADA:
% ✅ Distancia PK (no KP) en eje Y - CORREGIDO
% ✅ Curva exponencial decreciente cyan - CONFIRMADO
% ✅ Puntos cyan en inicio y final - AÑADIDOS
% ✅ Líneas punteadas de referencia - OPTIMIZADAS
% ✅ Etiquetas QP y h correctas - VERIFICADO
% ✅ Proporciones fieles a imagen original - AJUSTADO
% ✅ Compatible Qtikz/Ktikz - VERIFICADO

% FUNCIÓN MATEMÁTICA REPRESENTADA:
% f(α) = QP * exp(-k*α) + h
% Donde:
% - QP = valor inicial (máximo)
% - h = valor asintótico (mínimo)
% - k = constante de decaimiento
% - α = ángulo (variable independiente)
