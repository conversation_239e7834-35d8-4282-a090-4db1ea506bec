% Opción C - Gráfica con Discontinuidad (Compatible Qtikz/Ktikz)
% Basada en análisis de imagen con múltiples gráficas matemáticas
\begin{tikzpicture}[scale=0.8]
% Ejes principales
\draw[->] (0,0) -- (4,0) node[below] {Ángulo $\alpha$};
\draw[->] (0,0) -- (0,3) node[left] {Distancia $KP$};

% Primera parte de la curva (antes de la discontinuidad)
\draw[cyan, thick] (0.5,2.5) .. controls (1,2.2) .. (1.8,1.8);

% Punto de discontinuidad (círculo vacío)
\draw[cyan, thick] (1.8,1.8) circle (2pt);

% Segunda parte de la curva (después de discontinuidad)
\draw[cyan, thick] (2.2,1.5) .. controls (2.8,1.3) .. (3.5,1.1);

% Punto de inicio de segunda parte (círculo lleno)
\fill[cyan] (2.2,1.5) circle (2pt);

% Líneas punteadas de referencia
\draw[gray, dashed] (0.5,0) -- (0.5,2.5);
\draw[gray, dashed] (0,1.8) -- (1.8,1.8);
\draw[gray, dashed] (0,2.5) -- (0.5,2.5);

% Línea vertical que marca la discontinuidad
\draw[gray, dotted] (2,0) -- (2,2.5);

% Etiquetas específicas
\node[below] at (0.5,-0.2) {0};
\node[below] at (2,-0.2) {$\alpha_0$};
\node[below] at (3.5,-0.2) {$\alpha$};
\node[left] at (-0.2,2.5) {$QP$};
\node[left] at (-0.2,1.8) {$h$};

% Puntos extremos marcados
\fill[cyan] (0.5,2.5) circle (2pt);
\fill[cyan] (3.5,1.1) circle (2pt);
\end{tikzpicture}
