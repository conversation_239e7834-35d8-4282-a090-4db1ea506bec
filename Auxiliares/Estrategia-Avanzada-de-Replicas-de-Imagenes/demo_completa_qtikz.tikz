% DEMOSTRACIÓN COMPLETA - Réplica de imagen con múltiples gráficas
% Compatible con Qtikz/Ktikz - Basado en análisis automático de imagen
% Para usar en Qtikz/Ktikz: copiar solo el contenido del tikzpicture
% Para LaTeX completo: descomentar las líneas del documentclass

% Problema principal
\begin{tikzpicture}
\node[text width=12cm, align=center] at (6,8) {
    \textbf{6. Un punto $K$ se mueve de un extremo a otro del segmento $QT$ que se muestra en la gráfica.}
};

% Diagrama geométrico principal
\begin{scope}[shift={(3,6)}]
    % Segmento QT
    \draw[thick] (0,0) -- (4,0);
    \fill[black] (0,0) circle (2pt) node[below] {$Q$};
    \fill[black] (4,0) circle (2pt) node[below] {$T$};
    \fill[blue] (2,0) circle (2pt) node[below] {$K$};
    
    % Punto P
    \fill[black] (3,1.5) circle (2pt) node[above] {$P$};
    
    % Líneas
    \draw[blue] (2,0) -- (3,1.5);
    \draw[dashed] (3,0) -- (3,1.5);
    
    % Ángulo y medidas
    \draw[red] (0.3,0) arc (0:30:0.3);
    \node[red] at (0.5,0.1) {$\alpha$};
    \draw[<->] (3.2,0) -- (3.2,1.5);
    \node[right] at (3.3,0.75) {$h$};
\end{scope}

% Texto de la relación trigonométrica
\node[text width=12cm, align=center] at (6,5) {
    El ángulo $\alpha$ y la medida $h$ se relacionan mediante la razón trigonométrica $\sin(\alpha) = \frac{h}{KP}$, de donde se deduce \\
    la distancia entre $K$ y $P$ como $KP = \frac{h}{\sin(\alpha)}$ o $KP = h \times \csc(\alpha)$.
};

\node[text width=12cm, align=center] at (6,4) {
    ¿Cuál es la gráfica que muestra las distancias $KP$, cada vez que $K$ se mueve sobre el segmento $QT$?
};

% Opción A
\node at (1,3) {\textbf{A.}};
\begin{scope}[shift={(2,2.5)}, scale=0.6]
    \draw[->] (0,0) -- (3,0) node[below] {Ángulo $\alpha$};
    \draw[->] (0,0) -- (0,2) node[left] {Distancia $KP$};
    \draw[cyan, thick] (0.3,1.2) -- (2.7,1.2);
    \node[below] at (0.3,-0.1) {0};
    \node[below] at (2.7,-0.1) {$\alpha$};
    \node[left] at (-0.1,1.2) {$h$};
\end{scope}

% Opción B
\node at (1,1.5) {\textbf{B.}};
\begin{scope}[shift={(2,1)}, scale=0.6]
    \draw[->] (0,0) -- (3,0) node[below] {Ángulo $\alpha$};
    \draw[->] (0,0) -- (0,2) node[left] {Distancia $KP$};
    \draw[cyan, thick] (0.3,1.8) .. controls (1,1.5) and (2,1.2) .. (2.7,0.8);
    \draw[dashed] (0.3,0) -- (0.3,1.8);
    \node[below] at (0.3,-0.1) {0};
    \node[below] at (2.7,-0.1) {$\alpha$};
    \node[left] at (-0.1,1.8) {$QP$};
    \node[left] at (-0.1,0.8) {$h$};
\end{scope}

% Opción C
\node at (7,-0.5) {\textbf{C.}};
\begin{scope}[shift={(8,-1)}, scale=0.6]
    \draw[->] (0,0) -- (3,0) node[below] {Ángulo $\alpha$};
    \draw[->] (0,0) -- (0,2) node[left] {Distancia $KP$};
    \draw[cyan, thick] (0.3,1.8) .. controls (0.8,1.5) .. (1.3,1.2);
    \draw[cyan, thick] (1.7,1.0) .. controls (2.2,0.9) .. (2.7,0.8);
    \draw[cyan] (1.3,1.2) circle (1pt);
    \fill[cyan] (1.7,1.0) circle (1pt);
    \node[below] at (0.3,-0.1) {0};
    \node[below] at (2.7,-0.1) {$\alpha$};
    \node[left] at (-0.1,1.8) {$QP$};
    \node[left] at (-0.1,1.2) {$h$};
\end{scope}

% Opción D
\node at (7,1.5) {\textbf{D.}};
\begin{scope}[shift={(8,1)}, scale=0.6]
    \draw[->] (0,0) -- (3,0) node[below] {Ángulo $\alpha$};
    \draw[->] (0,0) -- (0,2) node[left] {Distancia $KP$};
    \draw[cyan, thick] (0.3,1.0) -- (2.7,1.0);
    \draw[dashed] (0.3,0) -- (0.3,1.0);
    \node[below] at (0.3,-0.1) {0};
    \node[below] at (2.7,-0.1) {$\alpha$};
    \node[left] at (-0.1,1.0) {$QP$};
\end{scope}

\end{tikzpicture}
